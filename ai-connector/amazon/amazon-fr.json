{"meta": {"schemaVersion": "1.0.0"}, "data": {"type": "blueprint", "attributes": {"getDocuments": [{"action": "goToUrl", "url": "https://www.amazon.fr/gp/css/order-history"}, {"action": "for<PERSON>ach", "id": "invoice-links-loop", "on": {"querySelector": "a[href*='/invoice']"}, "steps": [{"action": "click"}, {"action": "waitForNetworkIdle"}, {"action": "findPdfs", "outputVariable": "new_pdfs"}, {"action": "mergeVariables", "inputVariable": "new_pdfs", "outputVariable": "all_pdfs"}]}, {"action": "downloadPdfsFromUrls", "inputVariable": "all_pdfs"}]}}}